@import "tailwindcss";

/* Custom CSS Variables */
:root {
  --neon-green: #84ff00;
  --court-dark: #0a0a0a;
}

/* Base styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  background-color: var(--court-dark);
  color: white;
  line-height: 1.6;
}

/* Custom utility classes */
.bg-court-dark {
  background-color: var(--court-dark) !important;
}

.text-neon-green {
  color: var(--neon-green) !important;
}

.bg-neon-green {
  background-color: var(--neon-green) !important;
}

.border-neon-green {
  border-color: var(--neon-green) !important;
}

/* Container styles */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Button styles */
.btn-primary {
  background-color: var(--neon-green);
  color: black;
  padding: 0.75rem 2rem;
  border-radius: 0.375rem;
  font-weight: 700;
  text-transform: uppercase;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary:hover {
  background-color: #75e600;
}

.btn-outline {
  background-color: transparent;
  color: white;
  padding: 0.75rem 2rem;
  border-radius: 0.375rem;
  font-weight: 700;
  text-transform: uppercase;
  border: 2px solid white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-outline:hover {
  background-color: white;
  color: black;
}
