import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";

export default function Home() {
  return (
    <div className="min-h-screen bg-court-dark text-white">
      {/* Header/Navbar */}
      <header className="bg-court-dark border-b border-gray-800">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <div className="text-2xl font-bold text-white">COURT ONE</div>
          <div className="flex gap-4">
            <Button variant="outline" className="border-white text-white hover:bg-white hover:text-black">
              WHITEPAPER
            </Button>
            <Button className="bg-neon-green text-black hover:bg-neon-green/90 font-bold">
              Dashboard
            </Button>
          </div>
        </div>
      </header>

      {/* Hero Section 1 */}
      <section className="bg-court-dark py-20">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-8">
              <div className="flex items-center gap-2 text-white">
                <span>📍</span>
                <span>Indonesia</span>
              </div>
              <h1 className="text-4xl lg:text-6xl font-bold text-white leading-tight">
                THE WORLD&apos;S FIRST BLOCKCHAIN BACKED PADEL COURT
              </h1>
              <p className="text-lg text-gray-300 leading-relaxed">
                Own real courts. Earn passive income. Invest through NFTs and enjoy projected returns of up to 15% APR, backed by actual court revenue.
              </p>
              <div className="flex gap-4 flex-col sm:flex-row">
                <Button className="bg-white text-black hover:bg-gray-200 font-bold px-8 py-3">
                  Join Whitelist
                </Button>
                <Button variant="outline" className="border-white text-white hover:bg-white hover:text-black px-8 py-3">
                  Whitepaper
                </Button>
              </div>
            </div>
            <div className="flex justify-center">
              <Image
                src="/tennis.png"
                alt="Tennis Ball"
                width={400}
                height={400}
                className="w-full max-w-md"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Hero Section 2 */}
      <section className="relative py-32">
        <div className="absolute inset-0">
          <Image
            src="/Image1.png"
            alt="Padel Court"
            fill
            className="object-cover"
          />
          <div className="absolute inset-0 bg-black/70"></div>
        </div>
        <div className="relative container mx-auto px-4 text-center">
          <h2 className="text-4xl lg:text-6xl font-bold text-white mb-8">
            REAL COURTS. REAL RETURNS. ON-CHAIN.
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
            Court One is the world&apos;s first platform that&apos;s fully powered by blockchain technology,
            connecting real-world padel court investments with digital ownership through NFTs.
          </p>
        </div>
      </section>

      {/* How Does Court One Work */}
      <section className="bg-court-dark py-20">
        <div className="container mx-auto px-4">
          <h2 className="text-4xl lg:text-5xl font-bold text-neon-green text-center mb-16">
            HOW DOES COURT ONE WORKS?
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center space-y-6">
              <div className="text-neon-green text-6xl font-bold">01</div>
              <div className="flex justify-center">
                <Image src="/trading-card.png" alt="Investment" width={80} height={80} />
              </div>
              <h3 className="text-xl font-bold text-white">YOU INVEST</h3>
              <p className="text-gray-300">
                Mint a CourtOne NFT to claim your ownership stake
              </p>
            </div>
            <div className="text-center space-y-6">
              <div className="text-neon-green text-6xl font-bold">02</div>
              <div className="flex justify-center">
                <Image src="/padel.png" alt="Construction" width={80} height={80} />
              </div>
              <h3 className="text-xl font-bold text-white">WE BUILD</h3>
              <p className="text-gray-300">
                Premium padel courts in high-traffic areas in Indonesia
              </p>
            </div>
            <div className="text-center space-y-6">
              <div className="text-neon-green text-6xl font-bold">03</div>
              <div className="flex justify-center">
                <Image src="/wallet.png" alt="Earnings" width={80} height={80} />
              </div>
              <h3 className="text-xl font-bold text-white">YOU EARN</h3>
              <p className="text-gray-300">
                Enjoy passive income with profits distributed monthly
              </p>
            </div>
            <div className="text-center space-y-6">
              <div className="text-neon-green text-6xl font-bold">04</div>
              <div className="flex justify-center">
                <Image src="/padel2.png" alt="Playing" width={80} height={80} />
              </div>
              <h3 className="text-xl font-bold text-white">YOU PLAY</h3>
              <p className="text-gray-300">
                Get community perks and early access to special events
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Your NFT, Your Income */}
      <section className="bg-court-dark py-20">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-8">
              <h2 className="text-4xl lg:text-5xl font-bold text-neon-green">
                YOUR NFT, YOUR INCOME
              </h2>
              <p className="text-xl text-gray-300">
                Earn up to 15% APR from real padel court revenue
              </p>
              <div className="space-y-6">
                <div className="flex items-start gap-4">
                  <div className="text-neon-green text-2xl">✓</div>
                  <div>
                    <h3 className="text-lg font-bold text-white mb-2">Protected APR</h3>
                    <p className="text-gray-300">
                      Up to 15% annually based on actual court rental and operational profits
                    </p>
                  </div>
                </div>
                <div className="flex items-start gap-4">
                  <div className="text-neon-green text-2xl">✓</div>
                  <div>
                    <h3 className="text-lg font-bold text-white mb-2">Revenue Sources</h3>
                    <p className="text-gray-300">
                      Court bookings, equipment rentals, private events, and future F&B
                    </p>
                  </div>
                </div>
                <div className="flex items-start gap-4">
                  <div className="text-neon-green text-2xl">✓</div>
                  <div>
                    <h3 className="text-lg font-bold text-white mb-2">On-Chain Reporting</h3>
                    <p className="text-gray-300">
                      Transparent earnings via our real-time dashboard
                    </p>
                  </div>
                </div>
                <div className="flex items-start gap-4">
                  <div className="text-neon-green text-2xl">✓</div>
                  <div>
                    <h3 className="text-lg font-bold text-white mb-2">Monthly Payouts</h3>
                    <p className="text-gray-300">
                      Distributed directly to NFT holders
                    </p>
                  </div>
                </div>
              </div>
              <p className="text-sm text-gray-400">
                Returns are based on net profit and are subject to operational performance
              </p>
            </div>
            <div className="flex justify-center">
              <Image
                src="/hqmotion.gif"
                alt="NFT Animation"
                width={400}
                height={400}
                className="w-full max-w-md"
                unoptimized
              />
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="bg-court-dark py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-bold text-white mb-4">FAQ</h2>
            <p className="text-xl text-gray-300">
              Got questions? We&apos;ve got answers. Here&apos;s everything you need to know about Court One
            </p>
          </div>
          <div className="max-w-3xl mx-auto">
            <Accordion type="single" collapsible className="space-y-4">
              <AccordionItem value="item-1" className="border-gray-700">
                <AccordionTrigger className="text-white hover:text-neon-green text-left">
                  What is Court One and how does it work?
                </AccordionTrigger>
                <AccordionContent className="text-gray-300">
                  Court One is the world&apos;s first blockchain-backed padel court investment platform.
                  You purchase NFTs that represent ownership stakes in real padel courts, earning passive income
                  from court operations while enjoying community benefits.
                </AccordionContent>
              </AccordionItem>
              <AccordionItem value="item-2" className="border-gray-700">
                <AccordionTrigger className="text-white hover:text-neon-green text-left">
                  How are returns calculated and distributed?
                </AccordionTrigger>
                <AccordionContent className="text-gray-300">
                  Returns are based on actual court revenue including bookings, equipment rentals, and events.
                  Profits are calculated monthly and distributed directly to NFT holders based on their ownership percentage.
                </AccordionContent>
              </AccordionItem>
              <AccordionItem value="item-3" className="border-gray-700">
                <AccordionTrigger className="text-white hover:text-neon-green text-left">
                  What are the risks involved?
                </AccordionTrigger>
                <AccordionContent className="text-gray-300">
                  Like any investment, returns depend on operational performance. Court utilization, maintenance costs,
                  and market conditions can affect profitability. We provide transparent reporting to keep you informed.
                </AccordionContent>
              </AccordionItem>
              <AccordionItem value="item-4" className="border-gray-700">
                <AccordionTrigger className="text-white hover:text-neon-green text-left">
                  Can I play at the courts I invest in?
                </AccordionTrigger>
                <AccordionContent className="text-gray-300">
                  Yes! NFT holders get priority booking access, discounted rates, and exclusive access to special events
                  and tournaments at Court One facilities.
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          </div>
        </div>
      </section>

      {/* Call-to-Action Section */}
      <section className="relative py-32">
        <div className="absolute inset-0">
          <Image
            src="/image2.png"
            alt="Padel Court Action"
            fill
            className="object-cover"
          />
          <div className="absolute inset-0 bg-black/70"></div>
        </div>
        <div className="relative container mx-auto px-4 text-center">
          <h2 className="text-4xl lg:text-6xl font-bold text-white mb-8">
            STAKE IN THE COURT. EARN LIKE AN OWNER.
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-12 leading-relaxed">
            Invest in sport, earn from real players, and be part of a world-first innovation
          </p>
          <div className="flex gap-4 justify-center flex-col sm:flex-row">
            <Button className="bg-neon-green text-black hover:bg-neon-green/90 font-bold px-8 py-3 text-lg">
              Buy your Stake
            </Button>
            <Button variant="outline" className="border-white text-white hover:bg-white hover:text-black px-8 py-3 text-lg">
              Whitepaper
            </Button>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-court-dark border-t border-gray-800 py-12">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between items-center gap-8">
            <div className="text-center md:text-left">
              <p className="text-gray-400">
                COPYRIGHT © 2024 COURT ONE PADS. ALL RIGHTS RESERVED
              </p>
            </div>
            <div className="flex gap-6">
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                PRIVACY POLICY
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                TERMS & CONDITIONS
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                WHITEPAPER
              </a>
            </div>
            <div className="flex gap-4">
              <a href="#" className="text-gray-400 hover:text-neon-green transition-colors">
                <Image src="/xicon.png" alt="X" width={24} height={24} />
              </a>
              <a href="#" className="text-gray-400 hover:text-neon-green transition-colors">
                <Image src="/medium.png" alt="Medium" width={24} height={24} />
              </a>
              <a href="#" className="text-gray-400 hover:text-neon-green transition-colors">
                <span className="text-2xl">𝕏</span>
              </a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
