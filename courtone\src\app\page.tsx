import Image from "next/image";

export default function Home() {
  return (
    <div style={{ minHeight: '100vh', backgroundColor: '#0a0a0a', color: 'white' }}>
      {/* Header/Navbar */}
      <header style={{ backgroundColor: '#0a0a0a', borderBottom: '1px solid #374151', padding: '1rem 0' }}>
        <div className="container" style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div style={{ fontSize: '1.5rem', fontWeight: 'bold', color: 'white' }}>COURT ONE</div>
          <div style={{ display: 'flex', gap: '1rem' }}>
            <button className="btn-outline">WHITEPAPER</button>
            <button className="btn-primary">Dashboard</button>
          </div>
        </div>
      </header>

      {/* Hero Section 1 */}
      <section style={{ backgroundColor: '#0a0a0a', padding: '5rem 0' }}>
        <div className="container">
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '3rem', alignItems: 'center' }}>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '2rem' }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', color: 'white' }}>
                <span>📍</span>
                <span>Indonesia</span>
              </div>
              <h1 style={{ fontSize: 'clamp(2rem, 5vw, 4rem)', fontWeight: 'bold', color: 'white', lineHeight: '1.2' }}>
                THE WORLD&apos;S FIRST BLOCKCHAIN BACKED PADEL COURT
              </h1>
              <p style={{ fontSize: '1.125rem', color: '#d1d5db', lineHeight: '1.7' }}>
                Own real courts. Earn passive income. Invest through NFTs and enjoy projected returns of up to 15% APR, backed by actual court revenue.
              </p>
              <div style={{ display: 'flex', gap: '1rem', flexDirection: 'column' }}>
                <button className="btn-primary" style={{ alignSelf: 'flex-start' }}>Join Whitelist</button>
                <button className="btn-outline" style={{ alignSelf: 'flex-start' }}>Whitepaper</button>
              </div>
            </div>
            <div style={{ display: 'flex', justifyContent: 'center' }}>
              <Image
                src="/tennis.png"
                alt="Tennis Ball"
                width={400}
                height={400}
                style={{ width: '100%', maxWidth: '400px', height: 'auto' }}
              />
            </div>
          </div>
        </div>
      </section>

      {/* Hero Section 2 */}
      <section style={{ position: 'relative', padding: '8rem 0', backgroundImage: 'url(/Image1.png)', backgroundSize: 'cover', backgroundPosition: 'center' }}>
        <div style={{ position: 'absolute', inset: '0', backgroundColor: 'rgba(0, 0, 0, 0.7)' }}></div>
        <div className="container" style={{ position: 'relative', textAlign: 'center' }}>
          <h2 style={{ fontSize: 'clamp(2rem, 5vw, 4rem)', fontWeight: 'bold', color: 'white', marginBottom: '2rem' }}>
            REAL COURTS. REAL RETURNS. ON-CHAIN.
          </h2>
          <p style={{ fontSize: '1.25rem', color: '#d1d5db', maxWidth: '48rem', margin: '0 auto', lineHeight: '1.7' }}>
            Court One is the world&apos;s first platform that&apos;s fully powered by blockchain technology,
            connecting real-world padel court investments with digital ownership through NFTs.
          </p>
        </div>
      </section>

      {/* How Does Court One Work */}
      <section style={{ backgroundColor: '#0a0a0a', padding: '5rem 0' }}>
        <div className="container">
          <h2 style={{ fontSize: 'clamp(2rem, 4vw, 3rem)', fontWeight: 'bold', color: '#84ff00', textAlign: 'center', marginBottom: '4rem' }}>
            HOW DOES COURT ONE WORKS?
          </h2>
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '2rem' }}>
            <div style={{ textAlign: 'center', display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
              <div style={{ color: '#84ff00', fontSize: '4rem', fontWeight: 'bold' }}>01</div>
              <div style={{ display: 'flex', justifyContent: 'center' }}>
                <Image src="/trading-card.png" alt="Investment" width={80} height={80} />
              </div>
              <h3 style={{ fontSize: '1.25rem', fontWeight: 'bold', color: 'white' }}>YOU INVEST</h3>
              <p style={{ color: '#d1d5db' }}>
                Mint a CourtOne NFT to claim your ownership stake
              </p>
            </div>
            <div style={{ textAlign: 'center', display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
              <div style={{ color: '#84ff00', fontSize: '4rem', fontWeight: 'bold' }}>02</div>
              <div style={{ display: 'flex', justifyContent: 'center' }}>
                <Image src="/padel.png" alt="Construction" width={80} height={80} />
              </div>
              <h3 style={{ fontSize: '1.25rem', fontWeight: 'bold', color: 'white' }}>WE BUILD</h3>
              <p style={{ color: '#d1d5db' }}>
                Premium padel courts in high-traffic areas in Indonesia
              </p>
            </div>
            <div style={{ textAlign: 'center', display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
              <div style={{ color: '#84ff00', fontSize: '4rem', fontWeight: 'bold' }}>03</div>
              <div style={{ display: 'flex', justifyContent: 'center' }}>
                <Image src="/wallet.png" alt="Earnings" width={80} height={80} />
              </div>
              <h3 style={{ fontSize: '1.25rem', fontWeight: 'bold', color: 'white' }}>YOU EARN</h3>
              <p style={{ color: '#d1d5db' }}>
                Enjoy passive income with profits distributed monthly
              </p>
            </div>
            <div style={{ textAlign: 'center', display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
              <div style={{ color: '#84ff00', fontSize: '4rem', fontWeight: 'bold' }}>04</div>
              <div style={{ display: 'flex', justifyContent: 'center' }}>
                <Image src="/padel2.png" alt="Playing" width={80} height={80} />
              </div>
              <h3 style={{ fontSize: '1.25rem', fontWeight: 'bold', color: 'white' }}>YOU PLAY</h3>
              <p style={{ color: '#d1d5db' }}>
                Get community perks and early access to special events
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Your NFT, Your Income */}
      <section style={{ backgroundColor: '#0a0a0a', padding: '5rem 0' }}>
        <div className="container">
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '3rem', alignItems: 'center' }}>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '2rem' }}>
              <h2 style={{ fontSize: 'clamp(2rem, 4vw, 3rem)', fontWeight: 'bold', color: '#84ff00' }}>
                YOUR NFT, YOUR INCOME
              </h2>
              <p style={{ fontSize: '1.25rem', color: '#d1d5db' }}>
                Earn up to 15% APR from real padel court revenue
              </p>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
                <div style={{ display: 'flex', alignItems: 'flex-start', gap: '1rem' }}>
                  <div style={{ color: '#84ff00', fontSize: '1.5rem' }}>✓</div>
                  <div>
                    <h3 style={{ fontSize: '1.125rem', fontWeight: 'bold', color: 'white', marginBottom: '0.5rem' }}>Protected APR</h3>
                    <p style={{ color: '#d1d5db' }}>
                      Up to 15% annually based on actual court rental and operational profits
                    </p>
                  </div>
                </div>
                <div style={{ display: 'flex', alignItems: 'flex-start', gap: '1rem' }}>
                  <div style={{ color: '#84ff00', fontSize: '1.5rem' }}>✓</div>
                  <div>
                    <h3 style={{ fontSize: '1.125rem', fontWeight: 'bold', color: 'white', marginBottom: '0.5rem' }}>Revenue Sources</h3>
                    <p style={{ color: '#d1d5db' }}>
                      Court bookings, equipment rentals, private events, and future F&B
                    </p>
                  </div>
                </div>
                <div style={{ display: 'flex', alignItems: 'flex-start', gap: '1rem' }}>
                  <div style={{ color: '#84ff00', fontSize: '1.5rem' }}>✓</div>
                  <div>
                    <h3 style={{ fontSize: '1.125rem', fontWeight: 'bold', color: 'white', marginBottom: '0.5rem' }}>On-Chain Reporting</h3>
                    <p style={{ color: '#d1d5db' }}>
                      Transparent earnings via our real-time dashboard
                    </p>
                  </div>
                </div>
                <div style={{ display: 'flex', alignItems: 'flex-start', gap: '1rem' }}>
                  <div style={{ color: '#84ff00', fontSize: '1.5rem' }}>✓</div>
                  <div>
                    <h3 style={{ fontSize: '1.125rem', fontWeight: 'bold', color: 'white', marginBottom: '0.5rem' }}>Monthly Payouts</h3>
                    <p style={{ color: '#d1d5db' }}>
                      Distributed directly to NFT holders
                    </p>
                  </div>
                </div>
              </div>
              <p style={{ fontSize: '0.875rem', color: '#9ca3af' }}>
                Returns are based on net profit and are subject to operational performance
              </p>
            </div>
            <div style={{ display: 'flex', justifyContent: 'center' }}>
              <Image
                src="/hqmotion.gif"
                alt="NFT Animation"
                width={400}
                height={400}
                style={{ width: '100%', maxWidth: '400px', height: 'auto' }}
                unoptimized
              />
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section style={{ backgroundColor: '#0a0a0a', padding: '5rem 0' }}>
        <div className="container">
          <div style={{ textAlign: 'center', marginBottom: '4rem' }}>
            <h2 style={{ fontSize: 'clamp(2rem, 4vw, 3rem)', fontWeight: 'bold', color: 'white', marginBottom: '1rem' }}>FAQ</h2>
            <p style={{ fontSize: '1.25rem', color: '#d1d5db' }}>
              Got questions? We&apos;ve got answers. Here&apos;s everything you need to know about Court One
            </p>
          </div>
          <div style={{ maxWidth: '48rem', margin: '0 auto', display: 'flex', flexDirection: 'column', gap: '1rem' }}>
            <details style={{ backgroundColor: '#1f2937', padding: '1.5rem', borderRadius: '0.5rem', border: '1px solid #374151' }}>
              <summary style={{ fontSize: '1.125rem', fontWeight: 'bold', color: 'white', cursor: 'pointer', marginBottom: '1rem' }}>
                What is Court One and how does it work?
              </summary>
              <p style={{ color: '#d1d5db', lineHeight: '1.6' }}>
                Court One is the world&apos;s first blockchain-backed padel court investment platform.
                You purchase NFTs that represent ownership stakes in real padel courts, earning passive income
                from court operations while enjoying community benefits.
              </p>
            </details>
            <details style={{ backgroundColor: '#1f2937', padding: '1.5rem', borderRadius: '0.5rem', border: '1px solid #374151' }}>
              <summary style={{ fontSize: '1.125rem', fontWeight: 'bold', color: 'white', cursor: 'pointer', marginBottom: '1rem' }}>
                How are returns calculated and distributed?
              </summary>
              <p style={{ color: '#d1d5db', lineHeight: '1.6' }}>
                Returns are based on actual court revenue including bookings, equipment rentals, and events.
                Profits are calculated monthly and distributed directly to NFT holders based on their ownership percentage.
              </p>
            </details>
            <details style={{ backgroundColor: '#1f2937', padding: '1.5rem', borderRadius: '0.5rem', border: '1px solid #374151' }}>
              <summary style={{ fontSize: '1.125rem', fontWeight: 'bold', color: 'white', cursor: 'pointer', marginBottom: '1rem' }}>
                What are the risks involved?
              </summary>
              <p style={{ color: '#d1d5db', lineHeight: '1.6' }}>
                Like any investment, returns depend on operational performance. Court utilization, maintenance costs,
                and market conditions can affect profitability. We provide transparent reporting to keep you informed.
              </p>
            </details>
            <details style={{ backgroundColor: '#1f2937', padding: '1.5rem', borderRadius: '0.5rem', border: '1px solid #374151' }}>
              <summary style={{ fontSize: '1.125rem', fontWeight: 'bold', color: 'white', cursor: 'pointer', marginBottom: '1rem' }}>
                Can I play at the courts I invest in?
              </summary>
              <p style={{ color: '#d1d5db', lineHeight: '1.6' }}>
                Yes! NFT holders get priority booking access, discounted rates, and exclusive access to special events
                and tournaments at Court One facilities.
              </p>
            </details>
          </div>
        </div>
      </section>

      {/* Call-to-Action Section */}
      <section style={{ position: 'relative', padding: '8rem 0', backgroundImage: 'url(/image2.png)', backgroundSize: 'cover', backgroundPosition: 'center' }}>
        <div style={{ position: 'absolute', inset: '0', backgroundColor: 'rgba(0, 0, 0, 0.7)' }}></div>
        <div className="container" style={{ position: 'relative', textAlign: 'center' }}>
          <h2 style={{ fontSize: 'clamp(2rem, 5vw, 4rem)', fontWeight: 'bold', color: 'white', marginBottom: '2rem' }}>
            STAKE IN THE COURT. EARN LIKE AN OWNER.
          </h2>
          <p style={{ fontSize: '1.25rem', color: '#d1d5db', maxWidth: '48rem', margin: '0 auto 3rem auto', lineHeight: '1.7' }}>
            Invest in sport, earn from real players, and be part of a world-first innovation
          </p>
          <div style={{ display: 'flex', gap: '1rem', justifyContent: 'center', flexWrap: 'wrap' }}>
            <button className="btn-primary" style={{ fontSize: '1.125rem' }}>Buy your Stake</button>
            <button className="btn-outline" style={{ fontSize: '1.125rem' }}>Whitepaper</button>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer style={{ backgroundColor: '#0a0a0a', borderTop: '1px solid #374151', padding: '3rem 0' }}>
        <div className="container">
          <div style={{ display: 'flex', flexDirection: 'column', gap: '2rem', alignItems: 'center', textAlign: 'center' }}>
            <div>
              <p style={{ color: '#9ca3af' }}>
                COPYRIGHT © 2024 COURT ONE PADS. ALL RIGHTS RESERVED
              </p>
            </div>
            <div style={{ display: 'flex', gap: '1.5rem', flexWrap: 'wrap', justifyContent: 'center' }}>
              <a href="#" style={{ color: '#9ca3af', textDecoration: 'none' }}>PRIVACY POLICY</a>
              <a href="#" style={{ color: '#9ca3af', textDecoration: 'none' }}>TERMS & CONDITIONS</a>
              <a href="#" style={{ color: '#9ca3af', textDecoration: 'none' }}>WHITEPAPER</a>
            </div>
            <div style={{ display: 'flex', gap: '1rem' }}>
              <a href="#" style={{ color: '#9ca3af' }}>
                <Image src="/xicon.png" alt="X" width={24} height={24} />
              </a>
              <a href="#" style={{ color: '#9ca3af' }}>
                <Image src="/medium.png" alt="Medium" width={24} height={24} />
              </a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}

      {/* FAQ Section */}
      <section className="bg-court-dark py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-bold text-white mb-4">FAQ</h2>
            <p className="text-xl text-gray-300">
              Got questions? We&apos;ve got answers. Here&apos;s everything you need to know about Court One
            </p>
          </div>
          <div className="max-w-3xl mx-auto">
            <Accordion type="single" collapsible className="space-y-4">
              <AccordionItem value="item-1" className="border-gray-700">
                <AccordionTrigger className="text-white hover:text-neon-green text-left">
                  What is Court One and how does it work?
                </AccordionTrigger>
                <AccordionContent className="text-gray-300">
                  Court One is the world&apos;s first blockchain-backed padel court investment platform.
                  You purchase NFTs that represent ownership stakes in real padel courts, earning passive income
                  from court operations while enjoying community benefits.
                </AccordionContent>
              </AccordionItem>
              <AccordionItem value="item-2" className="border-gray-700">
                <AccordionTrigger className="text-white hover:text-neon-green text-left">
                  How are returns calculated and distributed?
                </AccordionTrigger>
                <AccordionContent className="text-gray-300">
                  Returns are based on actual court revenue including bookings, equipment rentals, and events.
                  Profits are calculated monthly and distributed directly to NFT holders based on their ownership percentage.
                </AccordionContent>
              </AccordionItem>
              <AccordionItem value="item-3" className="border-gray-700">
                <AccordionTrigger className="text-white hover:text-neon-green text-left">
                  What are the risks involved?
                </AccordionTrigger>
                <AccordionContent className="text-gray-300">
                  Like any investment, returns depend on operational performance. Court utilization, maintenance costs,
                  and market conditions can affect profitability. We provide transparent reporting to keep you informed.
                </AccordionContent>
              </AccordionItem>
              <AccordionItem value="item-4" className="border-gray-700">
                <AccordionTrigger className="text-white hover:text-neon-green text-left">
                  Can I play at the courts I invest in?
                </AccordionTrigger>
                <AccordionContent className="text-gray-300">
                  Yes! NFT holders get priority booking access, discounted rates, and exclusive access to special events
                  and tournaments at Court One facilities.
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          </div>
        </div>
      </section>

      {/* Call-to-Action Section */}
      <section className="relative py-32">
        <div className="absolute inset-0">
          <Image
            src="/image2.png"
            alt="Padel Court Action"
            fill
            className="object-cover"
          />
          <div className="absolute inset-0 bg-black/70"></div>
        </div>
        <div className="relative container mx-auto px-4 text-center">
          <h2 className="text-4xl lg:text-6xl font-bold text-white mb-8">
            STAKE IN THE COURT. EARN LIKE AN OWNER.
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-12 leading-relaxed">
            Invest in sport, earn from real players, and be part of a world-first innovation
          </p>
          <div className="flex gap-4 justify-center flex-col sm:flex-row">
            <Button className="bg-neon-green text-black hover:bg-neon-green/90 font-bold px-8 py-3 text-lg">
              Buy your Stake
            </Button>
            <Button variant="outline" className="border-white text-white hover:bg-white hover:text-black px-8 py-3 text-lg">
              Whitepaper
            </Button>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-court-dark border-t border-gray-800 py-12">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between items-center gap-8">
            <div className="text-center md:text-left">
              <p className="text-gray-400">
                COPYRIGHT © 2024 COURT ONE PADS. ALL RIGHTS RESERVED
              </p>
            </div>
            <div className="flex gap-6">
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                PRIVACY POLICY
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                TERMS & CONDITIONS
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                WHITEPAPER
              </a>
            </div>
            <div className="flex gap-4">
              <a href="#" className="text-gray-400 hover:text-neon-green transition-colors">
                <Image src="/xicon.png" alt="X" width={24} height={24} />
              </a>
              <a href="#" className="text-gray-400 hover:text-neon-green transition-colors">
                <Image src="/medium.png" alt="Medium" width={24} height={24} />
              </a>
              <a href="#" className="text-gray-400 hover:text-neon-green transition-colors">
                <span className="text-2xl">𝕏</span>
              </a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
